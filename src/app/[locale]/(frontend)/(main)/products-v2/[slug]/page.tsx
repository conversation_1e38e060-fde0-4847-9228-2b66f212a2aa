import { LivePreviewListener } from '@/components/LivePreviewListener'
import { PayloadRedirects } from '@/components/PayloadRedirects'
import { PORTAL_TOKEN_KEY } from '@/constants/global.constant'
import { IS_NATIVE_ENVIRONMENT } from '@/constants/storageKeys.constant'
import { ProductV2Detail } from '@/features/product-v2/components/ProductDetail/ProductV2Detail'
import { productV2Service } from '@/features/product-v2/services/product-v2.service'

import { MainPageWrapper } from '@/layouts/MainLayout/MainLayout/MainPageWrapper'
import { APP_ROUTES } from '@/routes'
import { generateMetaProductV2 } from '@/utilities/generateMeta'
import type { Metadata } from 'next'
import { cookies, draftMode } from 'next/headers'
import { notFound } from 'next/navigation'
import { cache } from 'react'
type Args = {
  params: Promise<{
    slug?: string
    locale: string
  }>
}

export default async function ProductV2DetailsPage({ params: paramsPromise }: Args) {
  const { isEnabled: draft } = await draftMode()
  const { slug = '', locale = 'vi' } = await paramsPromise
  const url = `${APP_ROUTES.PRODUCTS.children?.MEDICINES.path}/${slug}`
  const product = await queryProductV2BySlug({ slug, locale })
  const cookieStore = await cookies()
  const isNative = cookieStore.get(IS_NATIVE_ENVIRONMENT)?.value === 'true'
  if (!product) notFound()

  return (
    <MainPageWrapper withSubheader={false}>
      <ProductV2Detail product={product} isNative={isNative}></ProductV2Detail>
      <PayloadRedirects disableNotFound url={url} />
      {draft && <LivePreviewListener />}
    </MainPageWrapper>
  )
}

export async function generateMetadata({ params: paramsPromise }: Args): Promise<Metadata> {
  const { slug = '', locale = 'vi' } = await paramsPromise
  const product = await queryProductV2BySlug({ slug, locale })
  if (product) return generateMetaProductV2(product)
  return {}
}

const queryProductV2BySlug = cache(async ({ slug, locale }: { slug: string; locale: string }) => {
  const cookieStore = await cookies()
  const token = cookieStore.get(PORTAL_TOKEN_KEY)
  const result = await productV2Service.getProductV2BySlug({
    slug,
    options: {
      cache: 'no-store',
      next: {},
      headers: {
        Cookie: token ? `${token.name}=${token.value}` : '',
      } as HeadersInit,
    },
    params: {
      locale: locale,
      select: {
        patientGroup: true,
        specification: true,
        categories: true,
        isDietarySupplement: true,
        dietarySupplementCategories: true,
      },
    },
  })

  return result || null
})
