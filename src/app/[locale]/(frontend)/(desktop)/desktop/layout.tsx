'use client'
import AppSidebar from '@/components/SideBar/AppSidebar'
import { useSidebar } from '@/contexts/Sidebar/SidebarContext'
import MainHeader from '@/layouts/DesktopLayout/MainHeader/MainHeader'

export default function DesktopLayout({ children }: { children: React.ReactNode }) {
  const { isExpanded, isHovered, isMobileOpen } = useSidebar()

  // Dynamic class for main content margin based on sidebar state
  const mainContentMargin = isMobileOpen
    ? 'ml-0'
    : isExpanded || isHovered
      ? 'lg:ml-[300px] max-w-[calc(100vw-290px)]'
      : 'lg:ml-[90px] max-w-[calc(100vw-90px)]'

  return (
    <div className="min-h-screen xl:flex">
      {/* Sidebar and Backdrop */}
      <AppSidebar />
      {/* <Backdrop /> */}
      {/* Main Content Area */}
      <div className={`flex-1 transition-all duration-300 ease-in-out ${mainContentMargin}`}>
        {/* Header */}
        {/* <AppHeader /> */}

        <MainHeader />
        {/* Page Content - full-width */}
        <div>{children}</div>
      </div>
    </div>
  )
}
