'use-client'
import { But<PERSON> } from '@/components/ui/Button/Button'
import { useAuthentication } from '@/contexts/AuthenticationContext/AuthenticationContext'
import { Media } from '@/payload-types'
import { useTranslations } from 'next-intl'
import Image from 'next/image'
import React from 'react'

// image

import avatarDefault from '@/assets/icons/Avatar.svg'
import { cn } from '@/utilities/cn'
import Link from 'next/link'

const MainAuthentication: React.FC = () => {
  const t = useTranslations()
  const { status, user } = useAuthentication()
  const { oauthAvatar, avatar } = user || {}
  const { thumbnailURL } = (avatar as Media) || {}
  return (
    <React.Fragment>
      {user ? (
        <div className="flex items-center gap-x-2">
          {/* {currentUserSubscriptionPlanType === SubscriptionPlanType.FREE && <SubscriptionButton />} */}
          <Button variant={'blank'} className="p-0" disabled={status === 'loading'}>
            <Link href={status === 'unauthorized' ? '/auth/login' : '/user'}>
              <Image
                src={avatarDefault}
                alt="avatar"
                width={40}
                height={40}
                className={cn(
                  'size-8 shrink-0 cursor-pointer',
                  thumbnailURL || oauthAvatar ? 'rounded-full border border-divider' : '',
                )}
              ></Image>
            </Link>
          </Button>
        </div>
      ) : (
        <div className="flex items-center gap-3">
          <Button variant={'default'}>{t('MES-06')}</Button>

          <Button className="border-neutral-300" variant={'outline'}>
            {t('MES-09')}
          </Button>
        </div>
      )}
    </React.Fragment>
  )
}

export default MainAuthentication
