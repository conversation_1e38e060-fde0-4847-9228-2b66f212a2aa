'use client'

import React, { useEffect, useRef, useState } from 'react'
import { useLocale } from 'next-intl'
import { useRouter } from 'next/navigation'
import { routing, usePathname } from '@/config/i18nNavigation'
import Image from 'next/image'

// image
import japanFlag from '@/assets/icons/jp-circle.svg'
import viFlag from '@/assets/icons/vi-circle.svg'

export const LanguageDropdown: React.FC = () => {
  const [isOpen, setIsOpen] = useState(false)
  const dropdownRef = useRef<HTMLDivElement>(null)
  const router = useRouter()
  const pathname = usePathname()
  const locale = useLocale()

  // Language configuration with flags and names
  const languages = {
    ja: {
      code: 'ja',
      name: '日本語',
      flag: japanFlag,
      displayName: '日本語',
    },
    vi: {
      code: 'vi',
      name: 'Tiếng Việt',
      flag: viFlag,
      displayName: 'Tiếng Việt',
    },
  }

  const currentLanguage = languages[locale as keyof typeof languages] || languages.vi

  // Close dropdown when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
        setIsOpen(false)
      }
    }

    document.addEventListener('mousedown', handleClickOutside)
    return () => {
      document.removeEventListener('mousedown', handleClickOutside)
    }
  }, [])

  const handleLanguageChange = (languageCode: string) => {
    router.push(`/${languageCode}${pathname}`)
    router.refresh()
    setIsOpen(false)
  }

  return (
    <div className="relative" ref={dropdownRef}>
      {/* Dropdown Trigger */}
      <button
        type="button"
        onClick={() => setIsOpen(!isOpen)}
        className="focus:ring-primary/20 flex items-center gap-2 rounded-[99px] border border-border bg-neutral-100 px-3 py-2 text-sm font-medium text-foreground transition-colors hover:bg-accent focus:outline-none focus:ring-2"
        aria-label="Select language"
        aria-expanded={isOpen}
        aria-haspopup="true"
      >
        <div className="h-[20px] w-[20px]" style={{ borderRadius: '100%' }}>
          <Image
            // className="rounded-full"
            src={currentLanguage.flag}
            alt="flag"
            width={20}
            height={20}
          />
        </div>
        <span className="hidden sm:inline">{currentLanguage.name}</span>
        <svg
          className={`h-4 w-4 transition-transform duration-200 ${isOpen ? 'rotate-180' : ''}`}
          fill="none"
          stroke="currentColor"
          viewBox="0 0 24 24"
        >
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
        </svg>
      </button>

      {/* Dropdown Menu */}
      {isOpen && (
        <div className="absolute right-0 z-50 mt-2 w-48 rounded-lg border border-border bg-background shadow-lg">
          <div className="py-1">
            {routing.locales.map((languageCode) => {
              const language = languages[languageCode as keyof typeof languages]
              if (!language) {
                return null
              }

              const isSelected = languageCode === locale

              return (
                <button
                  type="button"
                  key={languageCode}
                  onClick={() => handleLanguageChange(languageCode)}
                  className={`flex w-full items-center gap-3 px-4 py-2 text-left text-sm transition-colors hover:bg-accent ${
                    isSelected ? 'bg-accent text-primary' : 'text-foreground'
                  }`}
                >
                  <Image
                    // className="rounded-full"
                    src={language.flag}
                    alt="flag"
                    width={20}
                    height={20}
                  />
                  <span className="flex-1">{language.displayName}</span>
                  {isSelected && (
                    <svg className="h-4 w-4 text-primary" fill="currentColor" viewBox="0 0 20 20">
                      <path
                        fillRule="evenodd"
                        d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
                        clipRule="evenodd"
                      />
                    </svg>
                  )}
                </button>
              )
            })}
          </div>
        </div>
      )}
    </div>
  )
}
