import { FavoriteKeyword } from '@/payload-types'
import { favoriteKeywordService } from '@/services/favorite-keyword.service'
import { Params } from '@/types/http.type'
import { UseQueryOptions, useQuery } from '@tanstack/react-query'
import { PaginatedDocs } from 'payload'
import { favoriteKeywordQueryKeys } from './queryKeys'

export const useGetFavoriteKeywords = ({
  params = {},
  options = {},
  useQueryOptions,
}: {
  params?: Params
  options?: RequestInit
  useQueryOptions?: Omit<
    UseQueryOptions<PaginatedDocs<FavoriteKeyword> | null>,
    'queryKey' | 'queryFn'
  >
} = {}) => {
  const {
    isError: isGetFavoriteKeywordsError,
    isPending: isGetFavoriteKeywordsLoading,
    data: favoriteKeywords,
    ...rest
  } = useQuery({
    queryKey: [favoriteKeywordQueryKeys['favorite-keywords'].base(), params],
    queryFn: async () =>
      favoriteKeywordService.getFavoriteKeywordsByUser({
        params: params,
        options: options,
      }),
    ...useQueryOptions,
  })
  return {
    isGetFavoriteKeywordsError,
    isGetFavoriteKeywordsLoading,
    favoriteKeywords,
    ...rest,
  }
}
