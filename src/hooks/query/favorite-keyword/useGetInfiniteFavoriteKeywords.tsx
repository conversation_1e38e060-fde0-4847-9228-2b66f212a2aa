import { FavoriteKeyword } from '@/payload-types'
import { favoriteKeywordService } from '@/services/favorite-keyword.service'
import { Params } from '@/types/http.type'
import { UseInfiniteQueryOptions, useInfiniteQuery } from '@tanstack/react-query'
import { PaginatedDocs } from 'payload'
import { favoriteKeywordQueryKeys } from './queryKeys'

export const useGetInfiniteFavoriteKeywords = ({
  params = {},
  options = {},
  key,
  overrideKey,
}: {
  params?: Params
  options?: RequestInit
  key?: string | number
  config?: Omit<
    UseInfiniteQueryOptions<PaginatedDocs<FavoriteKeyword>>,
    'queryFn' | 'queryKey' | ''
  >
  overrideKey?: string[]
} = {}) => {
  const {
    isError: isGetFavoriteKeywordsError,
    isFetching: isGetFavoriteKeywordsLoading,
    data: favoriteKeywords,
    fetchNextPage,
    hasNextPage,
    ...rest
  } = useInfiniteQuery({
    queryKey: overrideKey
      ? overrideKey
      : [favoriteKeywordQueryKeys['favorite-keywords'].base(), params, key],
    queryFn: ({ pageParam = 1 }) => {
      return favoriteKeywordService.getFavoriteKeywordsByUser({
        params: {
          ...params,
          page: pageParam,
        },
        options: options,
      })
    },
    getNextPageParam: (lastPage) => lastPage?.nextPage,
    initialPageParam: 1,
    getPreviousPageParam: (lastPage) => lastPage?.prevPage,
    // ...config,
  })

  return {
    isGetFavoriteKeywordsError,
    isGetFavoriteKeywordsLoading,
    favoriteKeywords,
    fetchNextPage,
    hasNextPage,
    ...rest,
  }
}
