import { MutationOptions, useMutation } from '@tanstack/react-query'
import { useRef } from 'react'
import { favoriteProductService } from '../../services/favorite-product.service'
import { favoriteProductMutationKeys } from './queryKeys'

type UpdateFavoriteProductVariables = { id: string; type: 'add' | 'delete' }

export const useUpdateFavoriteProduct = ({
  options,
}: {
  options?: Omit<MutationOptions | undefined, 'mutationFn' | 'mutationKey'>
} = {}) => {
  // Define an AbortController to cancel previous requests
  const abortControllerRef = useRef<AbortController | null>(null)

  const {
    isError: isUpdateFavoriteProductError,
    isPending: isUpdateFavoriteProductPending,
    mutate: updateFavoriteProductMutation,
    ...rest
  } = useMutation({
    mutationKey: favoriteProductMutationKeys['update-favorite-product'].base(),
    mutationFn: async ({ id, type }: UpdateFavoriteProductVariables) => {
      // Abort any ongoing request before initiating a new one
      if (abortControllerRef.current) {
        abortControllerRef.current.abort()
      }

      // Create a new AbortController for the new request
      const abortController = new AbortController()
      abortControllerRef.current = abortController

      // Pass the signal from AbortController to the service method
      if (type === 'add') {
        return favoriteProductService.addFavoriteProducts(id, {
          signal: abortControllerRef.current.signal,
        })
      } else if (type === 'delete') {
        return favoriteProductService.deleteFavoriteProducts(id, {
          signal: abortControllerRef.current.signal,
        })
      }

      return Promise.resolve()
    },
    ...options,
  })

  return {
    isUpdateFavoriteProductError,
    isUpdateFavoriteProductPending,
    updateFavoriteProductMutation,
    ...rest,
  }
}
