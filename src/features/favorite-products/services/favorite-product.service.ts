import { API_ENDPOINTS } from '@/constants/endpoint.constant'
import { httpService } from '@/services/http.service'
import { AxiosRequestConfig } from 'axios'

// SERVER / CLIENT
class FavoriteProductService {
  private static instance: FavoriteProductService

  private constructor() {}

  public static getInstance(): FavoriteProductService {
    if (!FavoriteProductService.instance) {
      FavoriteProductService.instance = new FavoriteProductService()
    }
    return FavoriteProductService.instance
  }

  public async addFavoriteProducts(id: string, options?: AxiosRequestConfig) {
    await httpService.post(`/${API_ENDPOINTS.favorite_products_api}/${id}`, undefined, options)
  }

  public async deleteFavoriteProducts(id: string, options?: AxiosRequestConfig) {
    await httpService.delete(`/${API_ENDPOINTS.favorite_products_api}/user/${id}`, options)
  }
}

export const favoriteProductService = FavoriteProductService.getInstance()
