'use client'

import { Skeleton } from '@/components/ui/Skeleton/Skeleton'
import { LocaleEnum } from '@/enums/locale.enum'
import {
  FilterField,
  useGenerateSearchQueryFilter,
} from '@/hooks/common/useGenerateSearchQueryFilter'
import { useGetPosts } from '@/hooks/query/post/useGetPosts'
import { cn } from '@/utilities/cn'
import { useLocale, useTranslations } from 'next-intl'
import { parse } from 'qs-esm'

import { PostCardItem } from '../PostCardItem/PostCardItem'
import { Post } from '@/payload-types'
import { PaginationWithQuery } from '@/components/ui/Pagination/PaginationWithQuery'
import { useCallback, useEffect, useRef, useState } from 'react'
import { useRouter } from 'next/navigation'
import PostsModalFilter from '../PostsModalFilter/PostsModalFilter'

interface PostsSearchProps {
  params?: {
    [key: string]: string | undefined
  }
}

const SEARCHABLE_FIELDS: FilterField[] = [
  { path: 'categories.title' },
  { path: 'keywords.keyword', type: 'like' },
  { path: 'title' },
]
export const PostsSearch: React.FC<PostsSearchProps> = ({ params: searchParams }) => {
  const t = useTranslations()
  const router = useRouter()

  const locale = useLocale()
  const searchKeywordsRef = useRef<string[]>([])

  const { page } = searchParams || {}
  const { q: query } = parse(searchParams as unknown as string)

  const [queryList, setListQuery] = useState<string[]>()

  const { queryFilters, generateQueryString } = useGenerateSearchQueryFilter({
    query,
    searchAbleFields: SEARCHABLE_FIELDS,
  })

  const getKeywordsFromParams = useCallback(
    (params?: { [key: string]: string | undefined }): string[] => {
      const keywords: string[] = []
      if (params) {
        Object.keys(params).forEach((key) => {
          if (key.startsWith('q[') && key.endsWith(']')) {
            const index = parseInt(key.slice(2, -1))
            if (!isNaN(index) && params[key]) {
              keywords[index] = params[key] as string
            }
          }
        })
      }
      return keywords.filter(Boolean)
    },
    [],
  )

  useEffect(() => {
    const keywords = getKeywordsFromParams(searchParams)
    searchKeywordsRef.current = keywords
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [searchParams])

  const { posts, isGetPostsLoading } = useGetPosts({
    params: {
      depth: 1,
      limit: 16,
      locale: locale || LocaleEnum.VI,
      fallbackLocale: false,
      page: page ? Number(page) : 1,
      where: {
        and: [
          {
            // _status: { equals: 'published' },
            language: { equals: locale || 'vi' },
            or: queryFilters,
          },
          {},
        ],
      },
      select: {
        title: true,
        heroImage: true,
        createdAt: true,
        id: true,
        slug: true,
        categories: true,
      },
    },
  })

  const handleFilter = ({ params, type }: { params: string[]; type: 'close' | 'filter' }) => {
    if (type === 'filter') {
      console.log(params)
    }
  }

  return isGetPostsLoading ? (
    <div className="mt-3 grid grid-cols-2 gap-3">
      {Array.from({ length: 8 }, (_, index) => (
        <div key={index} className={cn('relative flex gap-3')}>
          <Skeleton
            className={cn('relative h-[92px] w-[136px] overflow-hidden rounded-lg')}
          ></Skeleton>
          <div className="flex-1 space-y-2">
            <Skeleton className="h-5 w-8"></Skeleton>
            <Skeleton className="h-5 w-12"></Skeleton>
            <Skeleton className="h-8 w-full"></Skeleton>
          </div>
        </div>
      ))}
    </div>
  ) : (
    <>
      <div className="mt-3 flex items-center justify-between gap-2">
        <div className="typo-body-3">
          {t('MES-71')}: (<span>{posts?.totalDocs ?? 0}</span>)
        </div>
        <PostsModalFilter onFilter={handleFilter} />
      </div>

      <div className="mt-3 grid grid-cols-2 gap-3">
        {posts && posts?.docs?.length > 0 ? (
          posts.docs.map((post) => (
            <PostCardItem variant="row" post={post as Omit<Post, 'content'>} key={post.id} />
          ))
        ) : (
          <div className="typo-body-7 col-span-2 mt-3 h-[52px] w-full rounded-lg bg-danger-200 p-4 text-danger-600">
            {t('MES-72')}
          </div>
        )}
      </div>

      {/* Pagination */}
      {posts && posts?.docs?.length > 0 && posts?.totalPages >= 1 && (
        <div className="mt-4">
          <PaginationWithQuery
            totalPage={posts?.totalPages}
            initialPage={Number(page) || 1}
            queryUrl={{
              pathname: '/desktop/news',
            }}
            onChangePage={({ page }) => {
              const queryString = generateQueryString(searchKeywordsRef?.current || [])
              router.push('/desktop/news' + `?page=${page}${queryString ? `&${queryString}` : ''}`)
            }}
          ></PaginationWithQuery>
        </div>
      )}
    </>
  )
}
