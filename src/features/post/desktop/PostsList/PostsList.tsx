import React, { Suspense } from 'react'
import { getLocale, getTranslations } from 'next-intl/server'
import { LocaleEnum } from '@/enums/locale.enum'
import { postService } from '@/services/post.service'
import { FilteredPosts, FilteredPostsLoadingSkeleton } from '../FilteredPosts/FilteredPosts'
import { FilteredPostsSlider } from '../FilteredPosts/FilterPostsSlider/FilteredPostsSlider'

interface PostsListProps {
  params?: {
    [key: string]: string | undefined
  }
}

export const PostsList: React.FC<PostsListProps> = async ({ params }) => {
  const { categoryID, page } = params || {}
  const locale = await getLocale()
  const categories = await postService.getPostCategories({
    params: {
      collection: 'post-categories',
      pagination: false,
      depth: 1,
      // draft: false,
      locale: (locale as LocaleEnum) || 'vi',
    },
    options: {
      cache: 'no-store',
      // next: {
      //   revalidate: 600,
      // },
    },
  })

  const t = await getTranslations()

  return (
    <div className="space-y-6 pb-5">
      {/* Displayed only in the 'All Posts' filter */}
      {/* Newest Posts */}
      {!categoryID && (
        <>
          <Suspense
            fallback={
              <FilteredPostsLoadingSkeleton variant="column" isHiddenBannerSkeleton={true} />
            }
          >
            <FilteredPostsSlider
              options={{
                limit: 5,
                where: {
                  language: {
                    equals: locale as LocaleEnum,
                  },
                },
                sort: '-createdAt',
                depth: 1,
                locale: (locale as LocaleEnum) || 'vi',
                // draft: false,
                select: {
                  title: true,
                  id: true,
                  heroImage: true,
                  categories: true,
                  createdAt: true,
                  slug: true,
                },
              }}
              title={t('MES-68')}
              postCardVariant="column"
            ></FilteredPostsSlider>
          </Suspense>
          <div className="h-px w-full bg-neutral-100"></div>
        </>
      )}

      {/* Displayed only in the 'Category Posts' filter */}
      {/* Featured Posts */}
      {categoryID && (
        <Suspense
          fallback={<FilteredPostsLoadingSkeleton variant="column" isHiddenBannerSkeleton={true} />}
        >
          <FilteredPostsSlider
            options={{
              limit: 5,
              where: {
                'categories.id': { equals: categoryID },
                language: {
                  equals: locale as LocaleEnum,
                },
                featured: {
                  equals: true,
                },
              },
              sort: '-createdAt',
              depth: 1,
              locale: (locale as LocaleEnum) || 'vi',
              // draft: false,
              select: {
                title: true,
                id: true,
                heroImage: true,
                categories: true,
                createdAt: true,
                slug: true,
              },
            }}
            title={t('MES-69')}
            postCardVariant="column"
          ></FilteredPostsSlider>
        </Suspense>
      )}

      {/* Displayed only in the 'All Posts' filter */}
      {/* Short lists posts based on category */}
      {!categoryID &&
        categories &&
        categories?.docs?.map((category) => {
          return (
            <Suspense
              key={category.id}
              fallback={<FilteredPostsLoadingSkeleton isHiddenBannerSkeleton={true} />}
            >
              <FilteredPosts
                options={{
                  limit: 5,
                  where: {
                    'categories.id': { equals: category.id },
                    language: {
                      equals: locale as LocaleEnum,
                    },
                  },
                  depth: 1,
                  locale: (locale as LocaleEnum) || 'vi',
                  // draft: false,
                  select: {
                    title: true,
                    id: true,
                    heroImage: true,
                    categories: true,
                    createdAt: true,
                    slug: true,
                  },
                }}
                title={category.title}
                showAllQuery={{
                  categoryID: category.id,
                }}
                postCardVariant="row"
                isList={false}
              ></FilteredPosts>
            </Suspense>
          )
        })}

      {/* Displayed only in the 'Category Posts' filter */}
      {/* Lists posts based on category with pagination */}
      {categoryID && (
        <Suspense fallback={<FilteredPostsLoadingSkeleton isHiddenBannerSkeleton={true} />}>
          <FilteredPosts
            showPagination
            options={{
              limit: 16,
              where: {
                'categories.id': { equals: categoryID },
                language: {
                  equals: locale as LocaleEnum,
                },
              },
              sort: '-createdAt',
              depth: 1,
              locale: (locale as LocaleEnum) || 'vi',
              // draft: false,
              select: {
                title: true,
                id: true,
                heroImage: true,
                categories: true,
                createdAt: true,
                slug: true,
              },
              page: Number(page) || 1,
            }}
            postCardVariant="row"
            currentPage={page}
            categoryID={categoryID}
          ></FilteredPosts>
        </Suspense>
      )}
    </div>
  )
}
