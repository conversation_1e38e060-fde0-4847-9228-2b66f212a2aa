'use client'
import { But<PERSON> } from '@/components/ui/Button/Button'
import { useLocale, useTranslations } from 'next-intl'
import Image from 'next/image'

import CloseIcon from '@/assets/icons/exit.svg'
import { PostCategory } from '@/payload-types'
import { PaginatedDocs } from 'payload'
import { useEffect, useState } from 'react'
import { postService } from '@/services/post.service'
import { LocaleEnum } from '@/enums/locale.enum'
import { Skeleton } from '@/components/ui/Skeleton/Skeleton'

type PostFilterType = {
  close: (params?: any) => void
}
const PostsFilterClient: React.FC<PostFilterType> = ({ close }) => {
  const t = useTranslations()
  const locale = useLocale()

  const [selectedCategories, setSelectedCategories] = useState<Record<string, PostCategory>>({})

  const [categories, setCategories] = useState<PaginatedDocs<PostCategory> | null>(null)

  useEffect(() => {
    const getPostCategories = async () => {
      const res = await PostsFilter(locale as LocaleEnum)
      setCategories(res)
    }

    getPostCategories()
  }, [locale])

  const handleSelectCategory = (category: PostCategory) => {
    setSelectedCategories((prev) => {
      const newSelected = { ...prev }
      if (newSelected[category.id]) {
        delete newSelected[category.id]
      } else {
        newSelected[category.id] = category
      }
      return newSelected
    })
  }

  const applyFilter = () => {
    const categoriesSelected = Object.values(selectedCategories).map((t) => t.title)

    close({ params: categoriesSelected, type: 'filter' })
  }

  return (
    <div className="flex flex-col gap-4 bg-white p-6">
      <div className="flex items-center justify-between py-2">
        <span className="typo-heading-7">{t('MES-481')}</span>
        <Button onClick={close} variant={'blank'} className="p-0">
          <Image src={CloseIcon} alt={'close'} height={24} width={24} />
        </Button>
      </div>

      <div className="flex flex-1 flex-col gap-4 overflow-auto">
        <div className="flex flex-wrap items-center gap-3">
          <span className="typo-body-7 text-subdued">
            {t('MES-706')} ({Object.keys(selectedCategories).length}):
          </span>
          <div className="flex flex-wrap items-center gap-2">
            {Object.values(selectedCategories).map((category) => (
              <div
                key={category.id}
                className="flex items-center gap-3 rounded-md border border-primary-500 px-2 py-1 text-primary-500"
              >
                {category.title}
                <Image
                  className="cursor-pointer"
                  src={CloseIcon}
                  alt="remove"
                  height={16}
                  width={16}
                  onClick={() => handleSelectCategory(category)}
                />
              </div>
            ))}
          </div>
        </div>

        <div className="typo-body-10">{t('MES-736')}</div>

        {!categories ? (
          <div className="grid grid-cols-4 gap-3">
            {Array.from({ length: 8 }).map((_, idx) => (
              <Skeleton key={idx} className="min-h-14"></Skeleton>
            ))}
          </div>
        ) : (
          <div className="grid grid-cols-4 gap-3">
            {categories.docs.map((category) => {
              return (
                <div
                  onClick={() => handleSelectCategory(category)}
                  key={category.id}
                  className={`typo-body-6 $ flex min-h-14 cursor-pointer items-center justify-center rounded-lg border bg-neutral-200 ${selectedCategories[category.id] ? 'border-primary-500 text-primary-500' : 'border-transparent'}`}
                >
                  {category.title}
                </div>
              )
            })}
          </div>
        )}
      </div>

      <div className="flex items-center justify-end gap-4">
        <Button onClick={close} variant={'outline'} className="border-custom-neutral-200 px-4 py-2">
          {t('MES-105')}
        </Button>
        <Button onClick={applyFilter} className="px-4 py-2">
          {t('MES-281')}
        </Button>
      </div>
    </div>
  )
}

export default PostsFilterClient

const PostsFilter = async (locale: LocaleEnum) => {
  return await postService.getPostCategories({
    params: {
      pagination: false,
      depth: 1,
      // draft: false,
      locale: (locale as LocaleEnum) || 'vi',
      select: {
        title: true,
        id: true,
      },
    },
    options: {
      cache: 'no-store',
      // next: {
      //   revalidate: 600,
      // },
    },
  })
}
