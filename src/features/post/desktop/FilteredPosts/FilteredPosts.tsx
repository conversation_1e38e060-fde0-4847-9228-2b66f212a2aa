import { Skeleton } from '@/components/ui/Skeleton/Skeleton'
import { postService } from '@/services/post.service'
import { cn } from '@/utilities/cn'
import { isNil, omitBy } from 'lodash-es'
import { getTranslations } from 'next-intl/server'
import Link from 'next/link'
import { Options } from 'node_modules/payload/dist/collections/operations/local/find'
import { SelectExcludeType, SelectIncludeType } from 'payload'
import { ParsedUrlQueryInput } from 'querystring'
import React, { cache } from 'react'
import { FilterPostsPagination } from './FilterPostsPagination'
import { PostCardItem } from '../PostCardItem/PostCardItem'
interface FilteredPostsProps {
  options?: Omit<Options<'posts', SelectExcludeType | SelectIncludeType>, 'collection'>
  title?: string
  showAllQuery?: string | ParsedUrlQueryInput | null | undefined
  postCardVariant?: 'row' | 'column'
  showPagination?: boolean
  currentPage?: number | string
  categoryID?: string
  isList?: boolean
}

const getPostsCache = cache(
  async (options?: Omit<Options<'posts', SelectExcludeType | SelectIncludeType>, 'collection'>) => {
    try {
      const posts = await postService.getPosts({
        params: options,
        options: {
          next: {
            // revalidate: 60,
            // tags: ['posts'],
          },
          cache: 'no-store',
        },
      })
      return posts || null
    } catch (error) {
      console.log(error)
    }
  },
)

export const FilteredPosts: React.FC<FilteredPostsProps> = async ({
  options,
  title,
  showAllQuery,
  postCardVariant = 'column',
  showPagination = false,
  currentPage = 1,
  categoryID,
  isList = true,
}) => {
  const posts = await getPostsCache({
    ...options,
  })

  const t = await getTranslations()

  if (!posts || posts?.docs?.length === 0) return null

  const postView = posts.docs.slice(0, 2)
  const remainingPosts = posts.docs.slice(2)

  return (
    <div
      className={`space-y-3 px-4 ${showPagination ? '' : 'border-b border-neutral-100 pb-4 last:border-0 last:pb-0'} `}
    >
      {title && (
        <div className="flex items-center justify-between">
          {<span className="typo-body-3">{title}</span>}

          {showAllQuery && (
            <Link
              href={{
                pathname: '/desktop/news',
                query: omitBy(showAllQuery, isNil) as ParsedUrlQueryInput,
              }}
              className="typo-link-3 cursor-pointer text-primary"
            >
              {t('MES-141')}
            </Link>
          )}
        </div>
      )}
      {/* Lists */}
      {isList ? (
        // flex flex-col
        <div className="grid grid-cols-2 gap-3">
          {posts &&
            posts?.docs?.map((post) => {
              return (
                <PostCardItem variant={postCardVariant} post={post} key={post.id}></PostCardItem>
              )
            })}
        </div>
      ) : (
        <div className="grid grid-cols-3 gap-3">
          <div className="col-span-2 grid grid-cols-2 gap-3">
            {postView &&
              postView.map((post) => {
                return <PostCardItem variant={'column'} post={post} key={post.id}></PostCardItem>
              })}
          </div>
          <div className="flex flex-col gap-3">
            {remainingPosts &&
              remainingPosts.map((post) => {
                return (
                  <PostCardItem variant={postCardVariant} post={post} key={post.id}></PostCardItem>
                )
              })}
          </div>
        </div>
      )}
      {showPagination && posts.docs?.length > 0 && posts?.totalPages >= 1 && (
        <div className="mt-6">
          <FilterPostsPagination
            totalPage={posts?.totalPages}
            queryUrl={{
              pathname: '/desktop/news',
              query: {
                categoryID: categoryID,
              },
            }}
            initialPage={Number(currentPage) || 1}
          ></FilterPostsPagination>
        </div>
      )}
    </div>
  )
}

interface FilteredPostsLoadingSkeleton {
  variant?: 'row' | 'column'
  isHiddenBannerSkeleton?: boolean
}

export const FilteredPostsLoadingSkeleton: React.FC<FilteredPostsLoadingSkeleton> = ({
  variant = 'row',
  isHiddenBannerSkeleton = false,
}) => {
  return (
    <div className="px-4">
      {variant === 'row' ? (
        <div className={cn('relative flex gap-3')}>
          <Skeleton
            className={cn('relative h-[92px] w-[136px] overflow-hidden rounded-lg')}
          ></Skeleton>

          <div className="flex-1 space-y-2">
            <Skeleton className="h-5 w-8"></Skeleton>
            <Skeleton className="h-5 w-12"></Skeleton>
            <Skeleton className="h-8 w-full"></Skeleton>
          </div>
        </div>
      ) : (
        <div className={cn('relative flex flex-col gap-3')}>
          {!isHiddenBannerSkeleton && (
            <Skeleton
              className={cn('relative h-[197px] w-full overflow-hidden rounded-lg')}
            ></Skeleton>
          )}
          <div className="flex-1 space-y-2">
            <Skeleton className="h-5 w-14"></Skeleton>
            <Skeleton className="h-5 w-20"></Skeleton>
            <Skeleton className="h-8 w-full"></Skeleton>
          </div>
        </div>
      )}
    </div>
  )
}
