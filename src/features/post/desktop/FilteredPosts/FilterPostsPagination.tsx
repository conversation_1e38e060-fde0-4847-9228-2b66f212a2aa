'use client'
import { PaginationDynamic } from '@/components/ui/Pagination/PaginationDynamic'
import { useRouter } from 'next/navigation'
import { stringify } from 'qs-esm'
import React from 'react'
interface FilterPostsPaginationProps {
  totalPage: number
  initialPage?: number
  disabled?: boolean
  queryUrl: {
    pathname: string
    query?: {
      [key: string]: any
    }
  }
}
export const FilterPostsPagination: React.FC<FilterPostsPaginationProps> = ({
  totalPage,
  initialPage,
  queryUrl,
  disabled,
}) => {
  const router = useRouter()
  const handleChangePage = (page: number) => {
    router.push(
      queryUrl.pathname +
        '?' +
        stringify({
          ...queryUrl.query,
          page: page,
        }),
    )
  }
  return (
    <PaginationDynamic
      totalPage={totalPage}
      initialPage={initialPage || 1}
      onChange={(page) => {
        handleChangePage(page)
      }}
      disabled={disabled}
    ></PaginationDynamic>
  )
}
