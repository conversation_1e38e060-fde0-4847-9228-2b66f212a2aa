import { postService } from '@/services/post.service'
import { isNil, omitBy } from 'lodash-es'
import { getTranslations } from 'next-intl/server'
import Link from 'next/link'
import { Options } from 'node_modules/payload/dist/collections/operations/local/find'
import { SelectExcludeType, SelectIncludeType } from 'payload'
import { ParsedUrlQueryInput } from 'querystring'
import React, { cache } from 'react'
import { FilteredPostsSliderClient } from './FilteredPostsSlider.client'
interface FilteredPostsSliderProps {
  options?: Omit<Options<'posts', SelectExcludeType | SelectIncludeType>, 'collection'>
  title?: string
  showAllQuery?: string | ParsedUrlQueryInput | null | undefined
  postCardVariant?: 'row' | 'column'
  categoryID?: string
  isShowEmptyList?: boolean
}

const getPostsCache = cache(
  async (options?: Omit<Options<'posts', SelectExcludeType | SelectIncludeType>, 'collection'>) => {
    try {
      const posts = await postService.getPosts({
        params: options,
        options: {
          // next: {
          //   revalidate: 300,
          //   tags: ['posts'],
          // },
          cache: 'no-store',
        },
      })
      return posts || null
    } catch (error) {
      console.log(error)
    }
  },
)

export const FilteredPostsSlider: React.FC<FilteredPostsSliderProps> = async ({
  options,
  title,
  showAllQuery,
  postCardVariant = 'column',
  categoryID,
  isShowEmptyList = true,
}) => {
  const posts = await getPostsCache({
    ...options,
  })
  const t = await getTranslations()
  if (!posts || posts?.docs?.length === 0)
    return (
      <>
        {isShowEmptyList ? (
          <div className="typo-body-7 h-[52px] w-full rounded-lg bg-danger-200 p-4 text-danger-600">
            {t('MES-72')}
          </div>
        ) : null}
      </>
    )
  return (
    <div className="max-w-[calc(100vw-480px)] space-y-4 px-4">
      {title && (
        <div className="flex items-center justify-between">
          {<span className="typo-body-3">{title}</span>}

          {showAllQuery && (
            <Link
              href={{
                pathname: '/desktop/news',
                query: omitBy(showAllQuery, isNil) as ParsedUrlQueryInput,
              }}
              className="typo-link-3 cursor-pointer text-primary"
            >
              {t('MES-141')}
            </Link>
          )}
        </div>
      )}
      {/* Lists */}
      <FilteredPostsSliderClient
        posts={posts}
        postCardVariant={postCardVariant}
        categoryID={categoryID}
      ></FilteredPostsSliderClient>
    </div>
  )
}
