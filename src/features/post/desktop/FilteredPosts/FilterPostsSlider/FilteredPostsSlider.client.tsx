'use client'
import { Post } from '@/payload-types'
import { PaginatedDocs } from 'payload'
import React from 'react'
import { Swiper, SwiperSlide } from 'swiper/react'
import { PostCardItem } from '../../PostCardItem/PostCardItem'

interface FilteredPostsSliderClientProps {
  posts: PaginatedDocs<Post> | null | undefined
  postCardVariant?: 'row' | 'column'
  categoryID?: string
}
export const FilteredPostsSliderClient: React.FC<FilteredPostsSliderClientProps> = ({
  posts,
  postCardVariant = 'column',
}) => {
  return (
    <Swiper spaceBetween={15} slidesPerView={3.5}>
      {posts &&
        posts?.docs?.map((post) => (
          <SwiperSlide key={post.id}>
            <PostCardItem post={post} variant={postCardVariant}></PostCardItem>
          </SwiperSlide>
        ))}
    </Swiper>
  )
}
