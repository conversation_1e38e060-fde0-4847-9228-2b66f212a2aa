'use client'
import { MESSAGE_NATIVE } from '@/constants/message-native.constant'
import { useWebViewMessaging } from '@/hooks/common/useWebViewMessaging'
import { useDetectNativeEnvironment } from '@/hooks/native/useDetectNativeEnviroment'
import { Media, Post, PostCategory } from '@/payload-types'
import { cn } from '@/utilities/cn'
import { dateToYMD } from '@/utilities/date'
import Image from 'next/image'
import Link from 'next/link'
import React from 'react'
interface PostItemProps {
  post: Omit<Post, 'content'>
  variant?: 'row' | 'column'
  className?: string
  showCategory?: boolean
}
export const PostCardItem: React.FC<PostItemProps> = ({
  post,
  variant = 'column',
  className,
  showCategory = true,
}) => {
  const { isNative } = useDetectNativeEnvironment()
  const { sendMessage } = useWebViewMessaging()
  const { id, heroImage, title, categories, createdAt, slug } = post
  const { sizes } = (heroImage as Media) || {}
  const { title: categoryTitle } = (categories as PostCategory) || {}
  return (
    <Link
      onClick={(e) => {
        if (isNative) {
          e.preventDefault()
          sendMessage(MESSAGE_NATIVE.openPostDetail, {
            slug,
          })
        }
      }}
      href={`/desktop/news/${slug}`}
      key={id}
      className={cn('relative flex gap-3', variant === 'row' ? 'flex-row' : 'flex-col', className)}
    >
      {/* Image */}
      {sizes?.thumbnail?.url && (
        <div
          className={cn(
            'relative overflow-hidden rounded-lg',
            variant === 'row' ? 'h-[92px] w-[136px]' : 'h-[197px] w-full',
          )}
        >
          <Image
            src={sizes?.thumbnail?.url}
            alt={title}
            fill
            className="h-full w-full object-cover"
            sizes="400px"
          ></Image>
        </div>
      )}

      {/* Info */}

      <div className="flex-1 space-y-2">
        {showCategory && (
          <div className="typo-body-8 min-h-5 w-fit rounded-[4px] bg-primary-50 px-2 py-1 text-center text-primary">
            {categoryTitle}
          </div>
        )}
        <time className="typo-body-9 block">{dateToYMD(createdAt)}</time>

        <h3
          className={cn(
            'typo-body-3 line-clamp-2',
            variant === 'row' && 'typo-body-6 !font-medium',
          )}
        >
          {title}
        </h3>
      </div>
    </Link>
  )
}
