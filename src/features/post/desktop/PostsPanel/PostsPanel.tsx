import React from 'react'
import { getLocale } from 'next-intl/server'
import { LocaleEnum } from '@/enums/locale.enum'
import { PostsPanelClient } from './PostsPanel.client'
import { postService } from '@/services/post.service'

export const PostsPanel = async () => {
  const locale = await getLocale()
  const categories = await postService.getPostCategories({
    params: {
      pagination: false,
      depth: 1,
      // draft: false,
      locale: (locale as LocaleEnum) || 'vi',
      select: {
        title: true,
        id: true,
      },
    },
    options: {
      cache: 'no-store',
      // next: {
      //   revalidate: 600,
      // },
    },
  })

  return <>{categories && <PostsPanelClient categories={categories}></PostsPanelClient>}</>
}
