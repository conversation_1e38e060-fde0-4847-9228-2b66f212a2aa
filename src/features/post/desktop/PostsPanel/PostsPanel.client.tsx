'use client'
import { useSearchQuery } from '@/hooks/common/useSearchQuery'
import { PostCategory } from '@/payload-types'
import { cn } from '@/utilities/cn'
import { useTranslations } from 'next-intl'
import Link from 'next/link'
import { PaginatedDocs } from 'payload'
import React from 'react'
interface PostsPanelClientProps {
  categories: PaginatedDocs<PostCategory>
}
export const PostsPanelClient: React.FC<PostsPanelClientProps> = ({ categories }) => {
  const t = useTranslations()
  return (
    <div className="px-4">
      <div className="hide-scroll mb-6 mt-4 flex gap-x-3 overflow-x-auto">
        <PostCategoryItem
          category={{
            id: '',
            title: t('MES-20'),
          }}
        ></PostCategoryItem>
        {categories &&
          categories.docs?.map((category) => (
            <PostCategoryItem key={category.id} category={category}></PostCategoryItem>
          ))}
      </div>
    </div>
  )
}

interface PostCategoryItemProps {
  category: Pick<PostCategory, 'id' | 'title'>
}

const PostCategoryItem: React.FC<PostCategoryItemProps> = ({ category }) => {
  const { title, id } = category
  const { getAllSearchQueries } = useSearchQuery()
  const { categoryID } = getAllSearchQueries()

  return (
    <Link
      href={{
        pathname: '/desktop/news',
        query: id ? { categoryID: id } : undefined, // Add query only if `id` is truthy
      }}
      className={cn(
        'typo-body-6 cursor-pointer whitespace-nowrap rounded-md px-2 py-1',
        categoryID === id ? 'bg-primary text-white' : 'bg-custom-neutral-80 text-subdued',
        !categoryID && id === '' && '!bg-primary !text-white',
      )}
    >
      {title}
    </Link>
  )
}

export default PostCategoryItem
