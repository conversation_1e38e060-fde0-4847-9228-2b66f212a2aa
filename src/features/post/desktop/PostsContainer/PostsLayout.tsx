import React from 'react'
import { PostsContainer } from './PostsContainer'
import PostHeader from '../PostsHeader/PostsHeader'

interface PostsContainerProps {
  params?: {
    [key: string]: string | undefined
  }
}
export const PostsLayoutContainer: React.FC<PostsContainerProps> = ({ params }) => {
  return (
    <div className="bg-custom-background-hover px-16 py-6">
      <div className="rounded-3xl bg-white p-4">
        <PostHeader params={params} />
        <PostsContainer params={params} />
      </div>
    </div>
  )
}
