import React from 'react'
import { PostsPanel } from '../PostsPanel/PostsPanel'
import { PostsList } from '../PostsList/PostsList'
import { parse } from 'qs-esm'
import { PostsSearch } from '../PostsSearch/PostsSearch'

interface PostsContainerProps {
  params?: {
    [key: string]: string | undefined
  }
}
export const PostsContainer: React.FC<PostsContainerProps> = ({ params }) => {
  const { q: query } = parse(params as unknown as string)

  return query ? (
    <PostsSearch params={params} />
  ) : (
    <>
      <PostsPanel></PostsPanel>
      <PostsList params={params}></PostsList>
    </>
  )
}
