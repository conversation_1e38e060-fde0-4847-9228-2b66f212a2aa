'use client'
import { Input } from '@/components/ui/Input/Input'
import { useTranslations } from 'next-intl'
import { useRef, useState, useEffect, useCallback } from 'react'
import Image from 'next/image'
import { useDebounce } from '@/utilities/useDebounce'
import { useRouter } from 'next/navigation'
import {
  FilterField,
  useGenerateSearchQueryFilter,
} from '@/hooks/common/useGenerateSearchQueryFilter'
import { parse } from 'qs-esm'

// image
import SearchIcon from '@/assets/icons/search-normal.svg'
import CircleClose from '@/assets/icons/close-circle-gray-filled.svg'

const SEARCHABLE_FIELDS: FilterField[] = [
  { path: 'categories.title' },
  { path: 'keywords.keyword', type: 'like' },
  { path: 'title' },
]

interface PostHeaderProps {
  params?: {
    [key: string]: string | undefined
  }
}

const PostHeader: React.FC<PostHeaderProps> = ({ params }) => {
  const t = useTranslations()
  const router = useRouter()

  const isInitializedRef = useRef<boolean>(false)
  const [inputValue, setInputValue] = useState<string>('')
  const debouncedSearchValue = useDebounce(inputValue, 500)

  // Get current URL params to initialize search query filter
  const currentParams = typeof window !== 'undefined' ? window.location.search : ''
  const { q: query } = parse(currentParams)
  const { generateQueryString } = useGenerateSearchQueryFilter({
    query,
    searchAbleFields: SEARCHABLE_FIELDS,
  })

  const getKeywordsFromParams = useCallback(
    (params?: { [key: string]: string | undefined }): string[] => {
      const keywords: string[] = []
      if (params) {
        Object.keys(params).forEach((key) => {
          if (key.startsWith('q[') && key.endsWith(']')) {
            const index = parseInt(key.slice(2, -1))
            if (!isNaN(index) && params[key]) {
              keywords[index] = params[key] as string
            }
          }
        })
      }
      return keywords.filter(Boolean)
    },
    [],
  )

  useEffect(() => {
    const keywords = getKeywordsFromParams(params)
    handleListSearchKeywordsChange(keywords)
    setInputValue(keywords.join(' '))
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [])

  // Handle search keywords change and update router
  const handleListSearchKeywordsChange = useCallback(
    (keywords: string[]) => {
      const queryString = generateQueryString(keywords)

      // If no keywords, navigate to regular posts page
      if (keywords.length === 0) {
        const url = `/desktop/news`
        router.replace(url)
        return
      }

      // Navigate to search results with keywords
      const url = `/desktop/news?page=1${queryString ? `&${queryString}` : ''}`
      router.replace(url)
    },
    [generateQueryString, router],
  )

  useEffect(() => {
    if (!isInitializedRef.current) {
      return
    }
    // Convert the search value to keywords array and update router
    const keywords = debouncedSearchValue.trim() ? debouncedSearchValue.trim().split(/\s+/) : []
    handleListSearchKeywordsChange(keywords)
  }, [debouncedSearchValue, handleListSearchKeywordsChange])

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    isInitializedRef.current = true

    setInputValue(e.target.value)
  }

  const handleClearSearch = () => {
    setInputValue('')
    // Clear search in router as well
    handleListSearchKeywordsChange([])
  }

  return (
    <div className="flex items-center justify-between gap-2">
      <div className="typo-heading-7 text-primary-500">{t('MES-19')}</div>

      <div className="relative">
        <Input
          placeholder={t('MES-66')}
          className="min-w-[380px] rounded-lg px-4 py-2 pr-6"
          value={inputValue ?? ''}
          onChange={handleInputChange}
        />
        <Image
          alt={inputValue ? 'clear search' : 'search'}
          src={inputValue ? CircleClose : SearchIcon}
          width={20}
          height={20}
          className="absolute right-3 top-1/2 -translate-y-1/2 cursor-pointer"
          onClick={inputValue ? handleClearSearch : undefined}
        />
      </div>
    </div>
  )
}

export default PostHeader
