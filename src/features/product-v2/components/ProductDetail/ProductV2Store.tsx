'use client'

import { MESSAGE_NATIVE } from '@/constants/message-native.constant'
import { useWebViewMessaging } from '@/hooks/common/useWebViewMessaging'
import { useDetectNativeEnvironment } from '@/hooks/native/useDetectNativeEnviroment'
import { Media, MedicineBuyButton } from '@/payload-types'
import { useTranslations } from 'next-intl'
import Image from 'next/image'
import React from 'react'
export type MedStore = {
  'medicine-store'?: string | MedicineBuyButton | null | undefined
  url?: string | null | undefined
  id?: string | null | undefined
}

export const ProductV2Store = ({ filteredStores }: { filteredStores: MedStore[] }) => {
  const t = useTranslations()
  const { isNative } = useDetectNativeEnvironment()
  const { sendMessage } = useWebViewMessaging()
  return (
    <div className="mobile-wrapper fixed bottom-0 left-1/2 z-[100] -translate-x-1/2 border-divider bg-white">
      <div className="space-y-3 rounded-t-[24px] p-4 pb-6 shadow-[0px_4px_22px_0px_#00000026]">
        <p className="typo-body-7 text-subdued">{t('MES-714')}</p>
        <div className="flex gap-2">
          {filteredStores.map((store) => {
            const { url, id } = store
            const medicineStore = (store?.['medicine-store'] as unknown as MedicineBuyButton) || []
            const { title, logo } = medicineStore
            const { thumbnailURL, url: logoUrl } = (logo as Media) || {}
            const iconStoreUrl = thumbnailURL || logoUrl

            return (
              <React.Fragment key={id}>
                {title ? (
                  <a
                    key={id}
                    href={url!}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="typo-body-8 line-clamp-1 flex h-9 max-w-[106px] flex-1 items-center justify-center gap-x-2 rounded-[99px] border border-divider p-2"
                    onClick={(e) => {
                      if (isNative) {
                        e.preventDefault()
                        sendMessage(MESSAGE_NATIVE.openExternalLink, {
                          url,
                        })
                      }
                    }}
                  >
                    {iconStoreUrl && (
                      <Image
                        src={iconStoreUrl}
                        alt={title}
                        width={16}
                        height={16}
                        className="size-4 shrink-0 object-contain"
                        key={id}
                      ></Image>
                    )}

                    <span className="typo-body-8 truncate">{title}</span>
                  </a>
                ) : (
                  <></>
                )}
              </React.Fragment>
            )
          })}
        </div>
      </div>
    </div>
  )
}
