'use client'
import bookMarkIcon from '@/assets/icons/book-mark-icon.svg'
import profile2userIcon from '@/assets/icons/profile-2user-icon.svg'
import { Product, ProductAgeGroup, ProductCategory } from '@/payload-types'
import { useTranslations } from 'next-intl'
import Image from 'next/image'
import { PRODUCT_V2_TYPE_OPTIONS } from '../../constants'
interface ProductV2AdditionalInfoProps {
  product: Product
}
export const ProductV2AdditionalInfo = ({ product }: ProductV2AdditionalInfoProps) => {
  const t = useTranslations()
  const { ageGroups, type, categories } = product
  const categoriesSummary = categories
    ?.map((category) => (category as ProductCategory)?.title)
    .join(' - ')
  return (
    <div className="space-y-4 rounded-lg bg-custom-background-hover p-3">
      {/* Age Groups */}
      <div className="flex items-start gap-2">
        <Image
          src={profile2userIcon}
          alt="profile-2user-icon"
          width={20}
          height={20}
          className="size-5"
        />
        <div className="space-y-2">
          {ageGroups?.map((ageGroup) => {
            const { title, id } = ageGroup as ProductAgeGroup
            return (
              <div className="typo-body-7" key={id}>
                {title}
              </div>
            )
          })}
        </div>
      </div>

      {/* Type */}
      <div className="flex items-start gap-2">
        <Image
          src={PRODUCT_V2_TYPE_OPTIONS[type]?.icon}
          alt="product-v2-icon"
          width={20}
          height={20}
          className="size-5"
        />
        <div className="typo-body-7">{t(PRODUCT_V2_TYPE_OPTIONS[type]?.translationKey)}</div>
      </div>

      {/* Categories */}
      <div className="flex items-start gap-2">
        <Image
          src={bookMarkIcon}
          alt="profile-2user-icon"
          width={20}
          height={20}
          className="size-5"
        />
        <div className="typo-body-7">{categoriesSummary}</div>
      </div>
    </div>
  )
}
