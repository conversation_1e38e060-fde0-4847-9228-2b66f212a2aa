'use client'
import { useAuthentication } from '@/contexts/AuthenticationContext/AuthenticationContext'
import { favoriteProductQueryKeys } from '@/features/favorite-products/hooks/query/queryKeys'
import { useUpdateFavoriteProduct } from '@/features/favorite-products/hooks/query/useUpdateFavoriteProduct'
import { cn } from '@/utilities/cn'
import { useQueryClient } from '@tanstack/react-query'
import React, { useState } from 'react'

export const FavoriteProductButtonClient: React.FC<{ id: string; isFavorite: boolean }> = ({
  id,
  isFavorite,
}) => {
  const queryClient = useQueryClient()
  const [isFavoriteProduct, setIsFavoriteProduct] = useState<boolean>(Boolean(isFavorite))
  const { isUpdateFavoriteProductPending, updateFavoriteProductMutation } =
    useUpdateFavoriteProduct()

  const handleUpdateFavoriteProduct = async (e: React.MouseEvent, action: 'add' | 'delete') => {
    e.preventDefault()

    // Store the current state to revert if API fails
    const previousState = isFavoriteProduct

    // Immediately update the UI state
    if (action === 'add') {
      setIsFavoriteProduct(true)
    } else if (action === 'delete') {
      setIsFavoriteProduct(false)
    }

    updateFavoriteProductMutation(
      { id, type: action },
      {
        onSuccess: async () => {
          // Success - keep the new state
          queryClient.invalidateQueries({
            queryKey: [favoriteProductQueryKeys['favorite-products'].base()],
            type: 'active',
            refetchType: 'active',
          })
        },
        onError: (error) => {
          // Revert to previous state if API fails
          setIsFavoriteProduct(previousState)
          console.error('Error updating favorite medicine', error)
        },
      },
    )
  }

  const { user } = useAuthentication()

  return (
    <div className="shrink-0">
      {user && (
        <button
          disabled={isUpdateFavoriteProductPending}
          className={cn('p-1', isUpdateFavoriteProductPending && 'opacity-80')}
          onClick={(e) => handleUpdateFavoriteProduct(e, isFavoriteProduct ? 'delete' : 'add')}
        >
          <svg
            width={20}
            height={20}
            viewBox="0 0 20 20"
            fill={isFavoriteProduct ? '#EF4444' : 'none'}
            xmlns="http://www.w3.org/2000/svg"
          >
            <path
              d="M10.516 17.3418C10.2327 17.4418 9.76602 17.4418 9.48268 17.3418C7.06602 16.5168 1.66602 13.0752 1.66602 7.24183C1.66602 4.66683 3.74102 2.5835 6.29935 2.5835C7.81602 2.5835 9.15768 3.31683 9.99935 4.45016C10.841 3.31683 12.191 2.5835 13.6993 2.5835C16.2577 2.5835 18.3327 4.66683 18.3327 7.24183C18.3327 13.0752 12.9327 16.5168 10.516 17.3418Z"
              stroke="#EF4444"
              strokeWidth={1.5}
              strokeLinecap="round"
              strokeLinejoin="round"
            />
          </svg>
        </button>
      )}
    </div>
  )
}
