import { httpService } from '@/services/http.service'

import { Params } from '@/types/http.type'
import { API_ENDPOINTS } from '@/constants/endpoint.constant'
import { Product } from '@/payload-types'

// SERVER / CLIENT
class ProductV2Service {
  private static instance: ProductV2Service

  private constructor() {}

  public static getInstance(): ProductV2Service {
    if (!ProductV2Service.instance) {
      ProductV2Service.instance = new ProductV2Service()
    }
    return ProductV2Service.instance
  }

  public async getProductV2BySlug({
    slug,
    options = {},
    params = {},
  }: {
    slug: string
    options?: RequestInit
    params?: Params
  }): Promise<Product | null> {
    const data = await httpService.getWithFetch<Product | null>(
      `/${API_ENDPOINTS.products_v2_api}/details/${slug}`,
      params,
      options,
    )
    return data
  }
}

export const productV2Service = ProductV2Service.getInstance()
