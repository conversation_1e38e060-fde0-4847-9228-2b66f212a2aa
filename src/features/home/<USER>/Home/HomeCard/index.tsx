import { Button } from '@/components/ui/Button/Button'
import { useTranslations } from 'next-intl'
import Image from 'next/image'

// image
import AppleIcon from '@/assets/icons/apple-icon.svg'
import QRIcon from '@/assets/icons/qr-icon.svg'
import PreviewApp from '@/assets/images/previewPhone.png'
import { Section } from '@/features/home/<USER>/Section/Section'

const HomeCard: React.FC = () => {
  const t = useTranslations()

  return (
    <Section>
      <div className="flex justify-around rounded-xl bg-[linear-gradient(76deg,#1A64F9_14.6%,#80D8F2_67.44%,#CAE0FD_103.51%)] px-8 pt-4">
        <div className="flex flex-1 shrink-0 flex-col items-center justify-center gap-3 text-white">
          <div className="typo-heading-7">{t('MES-730')}</div>
          <div className="typo-body-7 text-center">{t('MES-731')}</div>
          <div className="flex items-center gap-4">
            <Button
              variant={'blank'}
              className="flex items-center gap-2 rounded-md bg-black px-4 py-2"
            >
              <Image src={AppleIcon} alt="apple" height={17} width={17} />
              <span className="typo-body-8 m-0 p-0 text-white">{t('MES-732')}</span>
            </Button>

            <Image src={QRIcon} alt="qr" height={70} width={70} />
          </div>
        </div>

        <div className="flex shrink-0 items-end justify-end">
          <Image src={PreviewApp} alt="preview" height={195} width={200} />
        </div>
      </div>
    </Section>
  )
}

export default HomeCard
