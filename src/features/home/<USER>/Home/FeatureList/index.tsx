import { Section } from '@/features/home/<USER>/Section/Section'
import { useTranslations } from 'next-intl'
import Image from 'next/image'
import FeatureBox from './FeatureBox'

// image
import InsuranceIcon from '@/assets/icons/insurance.svg'
import HospitalIcon from '@/assets/icons/hospital.svg'
import BodyIcon from '@/assets/icons/Body.svg'
import ScanIcon from '@/assets/icons/3d-cube-scan.svg'
import MedicalIcon from '@/assets/icons/edit-2.svg'

const FeatureList: React.FC = () => {
  const t = useTranslations()

  const featureList = [
    {
      name: t('MES-733'),
      source: HospitalIcon,
      alt: 'hospital icon',
    },
    {
      name: t('MES-36'),
      source: BodyIcon,
      alt: 'body icon',
    },
    {
      name: t('MES-734'),
      source: ScanIcon,
      alt: 'drugs icon',
    },
    {
      name: t('MES-735'),
      source: MedicalIcon,
      alt: 'medical icon',
    },
  ]
  return (
    <Section className="mt-2 px-0">
      <div className="typo-heading-8 flex items-center gap-2 text-primary-500">
        {t('MES-640')}

        <Image src={InsuranceIcon} alt="insurance-icon" height={24} width={24} />
      </div>

      <div className="mt-3 grid grid-cols-4 gap-3">
        {featureList.map((feature) => (
          <FeatureBox
            key={feature.alt}
            text={feature.name}
            source={feature.source}
            alt={feature.alt}
          />
        ))}
      </div>
    </Section>
  )
}

export default FeatureList
