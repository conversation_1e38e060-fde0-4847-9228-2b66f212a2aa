'use client'
import { TakeAPhotoIcon } from '@/components/Icons/TakeAPhotoIcon'
import { Button } from '@/components/ui/Button/Button'
import { Section } from '@/features/home/<USER>/Section/Section'
import { useTranslations } from 'next-intl'
import Image from 'next/image'

// image
import ExportIcon from '@/assets/icons/import.svg'

const SearchMedicineByImage: React.FC = () => {
  const t = useTranslations()

  const handleDownloadApplication = () => {}

  return (
    <Section className="h-fit px-0">
      <div
        style={{
          background: 'linear-gradient(105.05deg, #F79029 20.42%, #FDC661 98.24%)',
        }}
        className="relative space-y-4 overflow-hidden rounded-lg p-4 text-white"
      >
        <div className="flex flex-col gap-y-2">
          <div className="flex items-center justify-between gap-x-2">
            <div className="space-y-2">
              <h3 className="typo-body-3">{t('MES-728')}</h3>
            </div>
            <TakeAPhotoIcon className="size-[70px] shrink-0" />
          </div>
          <Button
            variant="blank"
            className="flex h-8 items-center gap-2 bg-white px-3 py-2 font-semibold !text-primary !opacity-100 disabled:!bg-white/90"
            onClick={() => handleDownloadApplication()}
          >
            <Image src={ExportIcon} alt={'export icon'} height={16} width={16} />
            <span className="text-xs font-medium">{t('MES-729')}</span>
          </Button>
        </div>
      </div>
    </Section>
  )
}

export default SearchMedicineByImage
