'use client'
import { NewspaperIcon } from '@/components/Icons/NewspaperIcon'
import { Button } from '@/components/ui/Button/Button'
import { Skeleton } from '@/components/ui/Skeleton/Skeleton'
import { CustomGAEvents } from '@/constants/event.constant'
import { LocaleEnum } from '@/enums/locale.enum'
import { Section } from '@/features/home/<USER>/Section/Section'
import { useGetPostCategories } from '@/hooks/query/post/useGetPostCategories'
import { useGetPosts } from '@/hooks/query/post/useGetPosts'
import { Media, Post, PostCategory } from '@/payload-types'
import { cn } from '@/utilities/cn'
import { dateToYMD } from '@/utilities/date'
import { sendGAEvent } from '@next/third-parties/google'
import { useLocale, useTranslations } from 'next-intl'
import Image from 'next/image'
import Link from 'next/link'
import { PaginatedDocs } from 'payload'
import React, { useCallback, useRef, useState } from 'react'
import type { Swiper as SwiperType } from 'swiper'
import { Swiper, SwiperSlide } from 'swiper/react'

const DEFAULT_POST_CATEGORY = 'ALL'
interface HomePostsClientProps {
  postsData: PaginatedDocs<Post> | null
  postCategoriesData: PaginatedDocs<PostCategory> | null
}

export const HomePostsClient: React.FC<HomePostsClientProps> = ({
  postsData,
  postCategoriesData,
}) => {
  const t = useTranslations()
  const locale = useLocale()
  const [selectedCategory, setSelectedCategory] = useState<string | null>(DEFAULT_POST_CATEGORY)
  const [isFirstLoad, setIsFirstLoad] = useState(true)
  const swiperRef = useRef<SwiperType | null>(null)

  // Get post categories
  const { postCategories, isGetPostCategoriesLoading } = useGetPostCategories({
    params: {
      pagination: false,
      locale: (locale as LocaleEnum) || 'vi',
    },
    useQueryOptions: {
      initialData: postCategoriesData,
      staleTime: 60000,
    },
  })

  // Create where condition for posts query
  const whereCondition = {
    language: { equals: locale as LocaleEnum },
    featured: { equals: true },
    ...(selectedCategory && selectedCategory !== DEFAULT_POST_CATEGORY
      ? { 'categories.id': { equals: selectedCategory } }
      : {}),
  }

  // Get posts based on selected category
  const { posts, isGetPostsLoading } = useGetPosts({
    params: {
      limit: 17,
      where: whereCondition,
      depth: 1,
      locale: (locale as LocaleEnum) || 'vi',
      // draft: false,
      sort: '-createdAt',
      select: {
        title: true,
        id: true,
        heroImage: true,
        categories: true,
        createdAt: true,
        slug: true,
      },
    },
    useQueryOptions: {
      initialData: isFirstLoad ? postsData : undefined,
      staleTime: 60000,
      //   enabled: !isGetPostCategoriesLoading,
    },
  })

  // // Set initial category from the first available category
  // useEffect(() => {
  //   if (!selectedCategory && postCategories && postCategories?.docs?.length > 0) {
  //     setSelectedCategory(postCategories.docs[0].id)
  //   }
  //   // eslint-disable-next-line react-hooks/exhaustive-deps
  // }, [postCategories])

  const handleCategorySelect = (categoryId: string) => {
    setIsFirstLoad(false)
    setSelectedCategory(categoryId)
    // Reset swiper to first slide when category changes
    if (swiperRef.current) {
      swiperRef.current.slideTo(0)
    }
  }

  // Get the first post (highlighted)
  const firstPost = posts?.docs?.[0]
  const viewPost = posts?.docs?.slice(0, 2)
  const prepareSlides = () => {
    if (!posts?.docs || posts.docs.length <= 1) return []

    const slides: Post[][] = []
    const totalPosts = posts.docs.length

    // Skip the first post and secondary post
    for (let i = 2; i < totalPosts; i += 4) {
      const slidePostsCount = Math.min(4, totalPosts - i)
      if (slidePostsCount > 0) {
        slides.push(posts.docs.slice(i, i + 4))
      }
    }

    return slides
  }

  const otherPosts = prepareSlides()

  const sendNavigationGAEvent = useCallback((name: string, url: string) => {
    sendGAEvent('event', CustomGAEvents.Navigation, {
      name: name,
      url: url,
      position: 'home-posts',
    })
  }, [])
  if (isGetPostCategoriesLoading && isGetPostsLoading) {
    return <HomePostsLoadingSkeleton />
  }

  return (
    <Section title={t('MES-548')} titleIcon={<NewspaperIcon />}>
      {/* Tab navigation */}
      <div className="hide-scroll mb-4 flex gap-2 overflow-x-auto">
        <Button
          variant={'blank'}
          className={cn(
            '!typo-body-6 h-9 rounded-full px-5 py-2',
            selectedCategory === DEFAULT_POST_CATEGORY
              ? 'border border-primary bg-white text-primary'
              : 'border-transparent bg-custom-neutral-80 text-subdued',
          )}
          onClick={() => handleCategorySelect(DEFAULT_POST_CATEGORY)}
        >
          {t('MES-20')}
        </Button>
        {postCategories?.docs?.map((category) => (
          <Button
            key={category.id}
            variant={'blank'}
            className={cn(
              'typo-body-6 h-9 rounded-full px-5 py-2',
              selectedCategory === category.id
                ? 'border border-primary bg-white text-primary'
                : 'border-transparent bg-custom-neutral-80 text-subdued',
            )}
            onClick={() => handleCategorySelect(category.id)}
          >
            {category?.title}
          </Button>
        ))}
      </div>
      {isGetPostsLoading ? (
        <PostSkeleton />
      ) : (
        <>
          {/* Highlight Post */}
          {viewPost?.length && (
            <div className="flex items-center gap-3">
              {viewPost.map((post) => (
                <Link
                  href={`/posts/${post.slug}`}
                  key={post.id}
                  className={cn('relative flex flex-1 shrink-0 flex-col gap-3 p-3')}
                  onClick={() => sendNavigationGAEvent(post.title, `/posts/${post.slug}`)}
                >
                  {/* Image */}
                  {(post.heroImage as Media)?.sizes?.thumbnail?.url && (
                    <div className={cn('relative h-[192px] w-full overflow-hidden')}>
                      <Image
                        src={(post.heroImage as Media)?.sizes?.thumbnail?.url as string}
                        alt={post.title}
                        fill
                        className="h-full w-full object-cover"
                        sizes="400px"
                      ></Image>
                    </div>
                  )}

                  {/* Info */}

                  <div className="flex-1 space-y-2">
                    <div className="typo-body-8 min-h-5 w-fit rounded-[4px] bg-primary-50 px-2 py-1 text-center text-primary">
                      {(post.categories as PostCategory)?.title}
                    </div>
                    <time className="typo-body-9 block text-subdued">
                      {dateToYMD(post.createdAt)}
                    </time>

                    <h3 className={cn('typo-body-6 line-clamp-2 uppercase')}>{post?.title}</h3>
                  </div>
                </Link>
              ))}
            </div>
          )}

          {/* List */}

          <Swiper
            spaceBetween={8}
            slidesPerView={2}
            onSwiper={(swiper) => {
              swiperRef.current = swiper
            }}
          >
            {otherPosts?.map((posts, index) => {
              return (
                <SwiperSlide key={index}>
                  <div className="flex flex-col divide-y divide-divider rounded-lg bg-custom-neutral-50 p-2">
                    {posts.map((post) => {
                      const { id, heroImage, title, createdAt, slug } = post
                      const { sizes } = (heroImage as Media) || {}

                      return (
                        <Link
                          href={`/posts/${slug}`}
                          key={id}
                          className={cn('relative flex items-center gap-3 px-2 py-3')}
                        >
                          {/* Image */}
                          {sizes?.thumbnail?.url && (
                            <div className={cn('relative overflow-hidden', 'h-[72px] w-[136px]')}>
                              <Image
                                src={sizes?.thumbnail?.url}
                                alt={title}
                                fill
                                className="h-full w-full object-cover"
                                sizes="400px"
                              ></Image>
                            </div>
                          )}

                          {/* Info */}

                          <div className="flex-1 space-y-2">
                            <time className="typo-body-9 block text-subdued">
                              {dateToYMD(createdAt)}
                            </time>

                            <h3 className={cn('typo-body-8 line-clamp-2')}>{title}</h3>
                          </div>
                        </Link>
                      )
                    })}
                  </div>
                </SwiperSlide>
              )
            })}
          </Swiper>
        </>
      )}
    </Section>
  )
}

export const HomePostsLoadingSkeleton = () => {
  return (
    <div className="space-y-3">
      {/* Tab skeleton */}
      <CategorySkeleton></CategorySkeleton>
      <PostSkeleton></PostSkeleton>
    </div>
  )
}

export const CategorySkeleton = () => {
  return (
    <div className="mb-4 flex gap-2">
      <Skeleton className="h-10 w-16 rounded-full"></Skeleton>
      <Skeleton className="h-10 w-16 rounded-full"></Skeleton>
      <Skeleton className="h-10 w-16 rounded-full"></Skeleton>
    </div>
  )
}

export const PostSkeleton = () => {
  return (
    <>
      <div className={cn('relative flex flex-col gap-3')}>
        <Skeleton className={cn('relative h-[197px] w-full overflow-hidden rounded-lg')}></Skeleton>
        <div className="flex-1 space-y-2">
          <Skeleton className="h-5 w-14"></Skeleton>
          <Skeleton className="h-5 w-20"></Skeleton>
          <Skeleton className="h-8 w-full"></Skeleton>
        </div>
      </div>
      <div className={cn('relative flex gap-3')}>
        <Skeleton
          className={cn('relative h-[92px] w-[136px] overflow-hidden rounded-lg')}
        ></Skeleton>
        <div className="flex-1 space-y-2">
          <Skeleton className="h-5 w-8"></Skeleton>
          <Skeleton className="h-5 w-12"></Skeleton>
          <Skeleton className="h-8 w-full"></Skeleton>
        </div>
      </div>
      <div className={cn('relative flex gap-3')}>
        <Skeleton
          className={cn('relative h-[92px] w-[136px] overflow-hidden rounded-lg')}
        ></Skeleton>
        <div className="flex-1 space-y-2">
          <Skeleton className="h-5 w-8"></Skeleton>
          <Skeleton className="h-5 w-12"></Skeleton>
          <Skeleton className="h-8 w-full"></Skeleton>
        </div>
      </div>
      <div className={cn('relative flex gap-3')}>
        <Skeleton
          className={cn('relative h-[92px] w-[136px] overflow-hidden rounded-lg')}
        ></Skeleton>
        <div className="flex-1 space-y-2">
          <Skeleton className="h-5 w-8"></Skeleton>
          <Skeleton className="h-5 w-12"></Skeleton>
          <Skeleton className="h-8 w-full"></Skeleton>
        </div>
      </div>
    </>
  )
}
