import { cache } from 'react'

import { LocaleEnum } from '@/enums/locale.enum'

import { postService } from '@/services/post.service'
import { getLocale } from 'next-intl/server'
import { Options } from 'node_modules/payload/dist/collections/operations/local/find'
import { SelectExcludeType, SelectIncludeType } from 'payload'
import { HomePostsClient } from './HomePosts.client'

export const HomePosts = async () => {
  const locale = await getLocale()
  const posts = await getPostsCache({
    limit: 17,
    where: {
      language: { equals: locale as LocaleEnum },
      featured: { equals: true },
    },
    depth: 1,
    locale: (locale as LocaleEnum) || 'vi',
    draft: false,
    sort: '-createdAt',
    select: {
      title: true,
      id: true,
      heroImage: true,
      categories: true,
      createdAt: true,
      slug: true,
    },
  })
  const postCategories = await getPostCategoriesCache(locale)

  return (
    <HomePostsClient
      postsData={posts || null}
      postCategoriesData={postCategories || null}
    ></HomePostsClient>
  )
}

const getPostsCache = cache(
  async (options?: Omit<Options<'posts', SelectExcludeType | SelectIncludeType>, 'collection'>) => {
    try {
      const posts = await postService.getPosts({
        params: options,
        options: {
          next: {
            // revalidate: 60,
            // tags: ['posts'],
          },
          cache: 'no-store',
        },
      })
      return posts || null
    } catch (error) {
      console.log(error)
    }
  },
)
const getPostCategoriesCache = cache(async (locale: string) => {
  const postCategories = await postService.getPostCategories({
    params: {
      pagination: false,
      locale: (locale as LocaleEnum) || 'vi',
      options: {
        cache: 'default',
        revalidate: 600,
      },
    },
  })
  return postCategories || null
})
