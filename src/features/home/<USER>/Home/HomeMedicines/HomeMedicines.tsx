'use client'
import arrow from '@/assets/icons/arrow-right.svg'
import fallbackIcon from '@/assets/icons/healthcare.svg'
import { DrugPillIcon } from '@/components/Icons/DrugPillIcon'
import { HealthcareIcon } from '@/components/Icons/HealthcareIcon'
import { But<PERSON> } from '@/components/ui/Button/Button'
import { Skeleton } from '@/components/ui/Skeleton/Skeleton'
import { CustomGAEvents } from '@/constants/event.constant'
import { Section } from '@/features/home/<USER>/Section/Section'
import { useGetBodyParts } from '@/hooks/query/body-part/useGetBodyPart'
import { useGetDietarySupplementCategories } from '@/hooks/query/dietary-supplement/useGetDietarySupplementCategories'
import { BodyPart, DietarySupplementCategory, Media } from '@/payload-types'
import { APP_ROUTES } from '@/routes'
import { cn } from '@/utilities/cn'
import { sendGAEvent } from '@next/third-parties/google'
import { useLocale, useTranslations } from 'next-intl'
import Image from 'next/image'
import Link from 'next/link'
import { PaginatedDocs } from 'payload'
import { useCallback, useEffect, useMemo, useRef, useState } from 'react'
import type { Swiper as SwiperType } from 'swiper'
import { Swiper, SwiperSlide } from 'swiper/react'

interface HomeMedicinesProps {
  bodyParts: PaginatedDocs<BodyPart> | null
}

const PANEL_ITEMS = {
  MEDICINE: {
    id: 'medicine',
    title: 'MES-557',
  },
  DIETARY_SUPPLEMENT: {
    id: 'Dietary Supplement',
    title: 'MES-471',
  },
}

export const HomeMedicines = ({ bodyParts }: HomeMedicinesProps) => {
  const locale = useLocale()
  const t = useTranslations()
  const [selectedCategory, setSelectedCategory] = useState<string | null>(PANEL_ITEMS.MEDICINE.id)
  const { bodyPart, isGetBodyPartLoading } = useGetBodyParts({
    params: {
      locale: locale,
      limit: 50,
      depth: 1,
      select: {
        name: true,
        id: true,
        heroImage: true,
      },
      where: {
        rootBodyPart: {
          equals: true,
        },
      },
    },
    useQueryOptions: {
      enabled: selectedCategory === PANEL_ITEMS.MEDICINE.id,
      initialData: bodyParts,
      staleTime: Infinity,
    },
  })
  const { dietarySupplementCategories, isGetDietarySupplementCategoriesLoading } =
    useGetDietarySupplementCategories({
      useQueryOptions: {
        enabled: selectedCategory === PANEL_ITEMS.DIETARY_SUPPLEMENT.id,
        staleTime: Infinity,
      },
      params: {
        limit: 25,
        depth: 1,
        draft: false,
        locale: locale,
      },
    })

  const isLoading =
    selectedCategory === PANEL_ITEMS.MEDICINE.id
      ? isGetBodyPartLoading
      : isGetDietarySupplementCategoriesLoading

  const swiperRef = useRef<SwiperType | null>(null)
  const [sliderState, setSliderState] = useState({
    isBeginning: true,
    isEnd: false,
  })
  const [totalSlides, setTotalSlides] = useState(0)

  const groupedBodyParts = useMemo(() => {
    const groups: BodyPart[][] = []
    for (let i = 0; i < (bodyPart?.docs?.length || 0); i += 3) {
      groups.push(bodyPart?.docs?.slice(i, i + 3) || [])
    }
    return groups
  }, [bodyPart])

  const groupedDietarySupplementCategories = useMemo(() => {
    const groups: DietarySupplementCategory[][] = []
    for (let i = 0; i < (dietarySupplementCategories?.docs?.length || 0); i += 3) {
      groups.push(dietarySupplementCategories?.docs?.slice(i, i + 3) || [])
    }
    return groups
  }, [dietarySupplementCategories])

  const sendNavigationGAEvent = useCallback((name: string, url: string) => {
    sendGAEvent('event', CustomGAEvents.Navigation, {
      name: name,
      url: url,
      position: 'home-medicines',
    })
  }, [])

  useEffect(() => {
    setSliderState({
      isBeginning: true,
      isEnd: false,
    })
  }, [selectedCategory])

  return (
    <Section className="relative" title={t('MES-558')} titleIcon={<DrugPillIcon />}>
      {totalSlides > 1 && (
        <NavigationButton
          onNextClick={() => swiperRef.current?.slideNext()}
          onPrevClick={() => swiperRef.current?.slidePrev()}
          isDisableNext={sliderState.isEnd}
          isDisabledPrev={sliderState.isBeginning}
        />
      )}

      {/* Tab navigation */}
      <div className="mb-4 flex gap-2 overflow-x-auto">
        {Object.values(PANEL_ITEMS).map((item) => (
          <Button
            key={item.id}
            variant={'blank'}
            className={cn(
              'typo-body-6 h-9 rounded-full px-5 py-2',
              selectedCategory === item.id
                ? 'border border-primary bg-white text-primary'
                : 'border-transparent bg-custom-neutral-80 text-subdued',
            )}
            onClick={() => setSelectedCategory(item.id)}
          >
            {t(item?.title)}
          </Button>
        ))}
      </div>

      <div className="relative">
        <Swiper
          key={selectedCategory}
          spaceBetween={8}
          slidesPerView={4}
          onSwiper={(swiper) => {
            swiperRef.current = swiper
            setTotalSlides(swiper.slides.length)
            setSliderState({
              isBeginning: swiper.isBeginning,
              isEnd: swiper.isEnd,
            })
          }}
          onReachBeginning={() => {
            setSliderState((prev) => ({ ...prev, isBeginning: true }))
          }}
          onReachEnd={() => {
            setSliderState((prev) => ({ ...prev, isEnd: true }))
          }}
          onFromEdge={() => {
            const swiper = swiperRef.current
            if (swiper) {
              setSliderState({
                isBeginning: swiper.isBeginning,
                isEnd: swiper.isEnd,
              })
            }
          }}
          className="gap-x-2"
        >
          {isLoading ? (
            <LoadingSkeleton />
          ) : selectedCategory === PANEL_ITEMS.MEDICINE.id ? (
            // Medicine (Body Parts) slides
            groupedBodyParts?.map((group, groupIndex) => (
              <SwiperSlide key={`body-part-group-${groupIndex}`}>
                <div className="flex flex-col gap-2">
                  {group.map((bodyPart, index) => {
                    const icon = bodyPart.heroImage as Media
                    const iconUrl = icon?.url
                    const name = bodyPart.name
                    const url = `${APP_ROUTES.PRODUCTS.children?.BODY_PARTS.path}/medicine?bodyPartId=${bodyPart.id}`
                    return (
                      <Link
                        key={bodyPart.id || index}
                        className="flex flex-col items-center justify-between gap-y-[6px] rounded-lg bg-primary-50 p-2 px-2 py-3"
                        href={url}
                        onClick={() => sendNavigationGAEvent(name, url)}
                      >
                        <div className="flex h-[50px] items-center justify-center p-2">
                          <Image
                            className="size-9 shrink-0"
                            src={iconUrl ?? fallbackIcon}
                            alt={bodyPart.name || 'body-part-icon'}
                            width={36}
                            height={36}
                          />
                        </div>
                        <span className="typo-body-6 line-clamp-1">{bodyPart.name}</span>
                      </Link>
                    )
                  })}
                </div>
              </SwiperSlide>
            ))
          ) : (
            // Dietary Supplement Categories slides
            groupedDietarySupplementCategories?.map((group, groupIndex) => (
              <SwiperSlide key={`supplement-group-${groupIndex}`}>
                <div className="flex flex-col gap-2">
                  {group.map((category, index) => {
                    const icon = category.image as Media
                    const iconUrl = icon?.url

                    return (
                      <Link
                        key={category.id || index}
                        className="flex flex-col items-center justify-between gap-y-[6px] rounded-lg p-2"
                        href={`${APP_ROUTES.PRODUCTS.children?.LIST.path}?s=true&sc=${category.id}`}
                      >
                        <div className="flex h-[50px] items-center justify-center rounded-lg bg-primary-50 p-2">
                          {iconUrl ? (
                            <Image
                              className="size-9 shrink-0"
                              src={iconUrl ?? fallbackIcon}
                              alt={category.title || 'supplement-category-icon'}
                              width={36}
                              height={36}
                              quality={100}
                              unoptimized
                            />
                          ) : (
                            <HealthcareIcon width={36} height={36} />
                          )}
                        </div>
                        <span className="typo-body-6 line-clamp-1">{category.title}</span>
                      </Link>
                    )
                  })}
                </div>
              </SwiperSlide>
            ))
          )}
        </Swiper>
      </div>
    </Section>
  )
}

const NavigationButton: React.FC<{
  onPrevClick: () => void
  onNextClick: () => void
  isDisabledPrev: boolean
  isDisableNext: boolean
}> = ({ isDisabledPrev, isDisableNext, onPrevClick, onNextClick }) => {
  return (
    <div className="absolute right-2 top-[-10px] flex items-center gap-3">
      <Button disabled={isDisabledPrev} onClick={onPrevClick} variant={'blank'} className="p-0">
        <Image className="rotate-180" src={arrow} alt="arrow left" height={20} width={20} />
      </Button>
      <Button disabled={isDisableNext} onClick={onNextClick} variant={'blank'} className="p-0">
        <Image src={arrow} alt="arrow right" height={20} width={20} />
      </Button>
    </div>
  )
}

const LoadingSkeleton = () => (
  <div className="grid grid-cols-3 gap-2">
    {Array(9)
      .fill(0)
      .map((_, index) => (
        <div
          key={index}
          className="flex flex-col items-center justify-between gap-y-[6px] rounded-lg p-2"
        >
          <Skeleton className="mx-auto size-[50px] rounded-lg" />
          <Skeleton className="h-4 w-16" />
        </div>
      ))}
  </div>
)
