'use client'
import arrow from '@/assets/icons/arrow-right.svg'
import { DocumentIcon } from '@/components/Icons/DocumentIcon'
import { HealthcareIcon } from '@/components/Icons/HealthcareIcon'
import { Button } from '@/components/ui/Button/Button'
import { CustomGAEvents } from '@/constants/event.constant'
import { Section } from '@/features/home/<USER>/Section/Section'
import { Faculty, Media } from '@/payload-types'
import { APP_ROUTES } from '@/routes'
import { sendGAEvent } from '@next/third-parties/google'
import { useTranslations } from 'next-intl'
import Image from 'next/image'
import Link from 'next/link'
import React, { useCallback, useMemo, useRef, useState } from 'react'
import type { Swiper as SwiperType } from 'swiper'
import { Swiper, SwiperSlide } from 'swiper/react'
interface HomeFacultiesProps {
  faculties: Faculty[]
}

export const HomeFaculties: React.FC<HomeFacultiesProps> = ({ faculties }) => {
  const t = useTranslations()
  const swiperRef = useRef<SwiperType | null>(null)
  const [sliderState, setSliderState] = useState({
    isBeginning: true,
    isEnd: false,
  })

  const groupedFaculties = useMemo(() => {
    const groups: Faculty[][] = []
    for (let i = 0; i < faculties.length; i += 4) {
      groups.push(faculties.slice(i, i + 4))
    }
    return groups
  }, [faculties])
  const sendNavigationGAEvent = useCallback((name: string, url: string) => {
    sendGAEvent('event', CustomGAEvents.Navigation, {
      name: name,
      url: url,
      position: 'home-faculties',
    })
  }, [])
  return (
    <Section className="relative" title={t('MES-556')} titleIcon={<DocumentIcon />}>
      <NavigationButton
        onNextClick={() => swiperRef.current?.slideNext()}
        onPrevClick={() => swiperRef.current?.slidePrev()}
        isDisableNext={sliderState.isEnd}
        isDisabledPrev={sliderState.isBeginning}
      />
      <div className="relative overflow-hidden">
        <Swiper
          spaceBetween={8}
          slidesPerView={4}
          onSwiper={(swiper) => {
            swiperRef.current = swiper
            setSliderState({
              isBeginning: swiper.isBeginning,
              isEnd: swiper.isEnd,
            })
          }}
          onReachBeginning={() => {
            setSliderState((prev) => ({ ...prev, isBeginning: true }))
          }}
          onReachEnd={() => {
            setSliderState((prev) => ({ ...prev, isEnd: true }))
          }}
          onFromEdge={() => {
            const swiper = swiperRef.current
            if (swiper) {
              setSliderState({
                isBeginning: swiper.isBeginning,
                isEnd: swiper.isEnd,
              })
            }
          }}
          className="gap-x-2"
        >
          {groupedFaculties?.map((group, groupIndex) => (
            <SwiperSlide key={`faculty-group-${groupIndex}`}>
              <div className="flex flex-col gap-2">
                {group.map((faculty, index) => {
                  const icon = faculty.icon as Media
                  const iconUrl = icon?.url
                  const name = faculty.name
                  const url = `${APP_ROUTES.MEDICAL_HANDBOOK.children?.FACULTIES.path}/${faculty.id}`
                  return (
                    <Link
                      key={faculty.id || index}
                      className="flex h-[50px] items-center gap-x-2 rounded-lg bg-primary-50 px-3 py-2"
                      href={url}
                      onClick={() => sendNavigationGAEvent(name, url)}
                    >
                      {iconUrl ? (
                        <Image
                          className="size-[32px] shrink-0"
                          src={iconUrl}
                          alt={icon?.alt || name || 'faculty-icon'}
                          width={32}
                          height={32}
                        />
                      ) : (
                        <HealthcareIcon width={32} height={32} />
                      )}
                      <span className="typo-body-6 line-clamp-1">{name}</span>
                    </Link>
                  )
                })}
              </div>
            </SwiperSlide>
          ))}
        </Swiper>
      </div>
    </Section>
  )
}

const NavigationButton: React.FC<{
  onPrevClick: () => void
  onNextClick: () => void
  isDisabledPrev: boolean
  isDisableNext: boolean
}> = ({ isDisabledPrev, isDisableNext, onPrevClick, onNextClick }) => {
  return (
    <div className="absolute right-2 top-[-10px] flex items-center gap-3">
      <Button disabled={isDisabledPrev} onClick={onPrevClick} variant={'blank'} className="p-0">
        <Image className="rotate-180" src={arrow} alt="arrow left" height={20} width={20} />
      </Button>
      <Button disabled={isDisableNext} onClick={onNextClick} variant={'blank'} className="p-0">
        <Image src={arrow} alt="arrow right" height={20} width={20} />
      </Button>
    </div>
  )
}
