import { authenticatedWithValidSession } from '@/access/authenticatedWithValidSession'
import { withErrorHandling } from '@/utils/errorHandlers'
import { APIError, PayloadRequest } from 'payload'

const removeFavoriteProductHandler = async (req: PayloadRequest) => {
  const isValid = await authenticatedWithValidSession({ req })

  if (!isValid) {
    throw new APIError('MES-448', 401)
  }

  // Validate the incoming request object
  if (!req || !req.routeParams) {
    throw new APIError('Request or route parameters are missing.', 400)
  }

  const { id } = req.routeParams

  if (!id) {
    throw new APIError('Missing ID', 400)
  }

  await req.payload.delete({
    collection: 'favorite-products',
    where: {
      'product.id': { equals: id },
    },
  })

  return Response.json({ message: 'Remove favorite product successfully' }, { status: 200 })
}

// Wrap the handler with standardized error handling
export const removeFavoriteProduct = withErrorHandling(
  removeFavoriteProductHandler,
  'An unexpected error occurred while removing favorite product',
)
