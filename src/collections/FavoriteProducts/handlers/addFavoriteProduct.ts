import { authenticatedWithValidSession } from '@/access/authenticatedWithValidSession'
import { EXCEPTION_STATUS } from '@/constants/exceptionStatus.constant'
import { withErrorHandling } from '@/utils/errorHandlers'
import { APIError, PayloadRequest } from 'payload'

const addFavoriteProductHandler = async (req: PayloadRequest) => {
  // Let authenticatedWithValidSession throw its own error if authentication fails
  const isValid = await authenticatedWithValidSession({ req })

  if (!isValid) {
    throw new APIError('MES-448', EXCEPTION_STATUS.SESSION_INVALID.code)
  }

  const user = req.user!

  // Validate the incoming request object
  if (!req || !req.routeParams) {
    throw new APIError('Request or route parameters are missing.', 400)
  }

  const { id } = req.routeParams

  if (!id) {
    throw new APIError('Missing product ID', 400)
  }

  const product = await req.payload.findByID({
    collection: 'products',
    id: id as string,
  })

  const favoriteProduct = await req.payload.find({
    collection: 'favorite-products',
    where: {
      'product.id': { equals: id },
      'user.id': {
        equals: user?.id,
      },
    },
    limit: 1,
    pagination: false,
    select: { product: true, user: true },
  })

  if (favoriteProduct?.docs.length) {
    throw new APIError('Can not add this product to favorite', 400)
  }

  await req.payload.create({
    collection: 'favorite-products',
    data: {
      user: user.id,
      product: product,
      name: `${user.name} - ${product.title}`,
    },
  })

  return Response.json({ message: 'Favorite product successfully' }, { status: 201 })
}

// Wrap the handler with standardized error handling
export const addFavoriteProduct = withErrorHandling(
  addFavoriteProductHandler,
  'An unexpected error occurred while adding favorite product',
)
