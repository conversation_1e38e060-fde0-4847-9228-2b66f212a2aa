import { adminOrCreated<PERSON><PERSON> } from '@/access/adminOrCreatedBy'
import { adminOrUser } from '@/access/adminOrUser'
import { admins } from '@/access/admins'
import { CollectionConfig } from 'payload'
import { addFavoriteProduct } from './handlers/addFavoriteProduct'
import { getFavoriteProductByUser } from './handlers/getFavoriteProductByUser'
import { removeFavoriteProduct } from './handlers/removeFavoriteProduct'

export const FavoriteProducts: CollectionConfig = {
  slug: 'favorite-products',
  access: {
    create: adminOrUser,
    update: admins,
    delete: adminOrCreatedBy,
    read: adminOrCreatedBy,
  },
  admin: {
    group: 'Users',
    useAsTitle: 'name',
  },
  fields: [
    {
      name: 'name',
      type: 'text',
    },
    {
      name: 'user',
      type: 'relationship',
      relationTo: 'users',
    },
    {
      name: 'product',
      type: 'relationship',
      relationTo: 'products',
      required: true,
    },
  ],
  endpoints: [
    {
      path: '/:id',
      method: 'post',
      handler: addFavoriteProduct,
    },
    {
      path: '/user',
      method: 'get',
      handler: getFavoriteProductByUser,
    },
    {
      path: '/user/:id',
      method: 'delete',
      handler: removeFavoriteProduct,
    },
  ],
}
